/* 导航菜单修复样式 */

/* 确保导航菜单容器可见 */
.header-r {
  display: flex !important;
  align-items: center !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 基础导航菜单样式 */
.nav-menu {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 桌面端显示菜单 */
@media (min-width: 769px) {
  .nav-menu {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    gap: 30px !important;
  }
}

/* 桌面端菜单项样式 */
.nav-menu li {
  position: relative !important;
  display: list-item !important;
}

.nav-menu a {
  text-decoration: none !important;
  color: #333 !important;
  font-weight: 500 !important;
  padding: 10px 0 !important;
  transition: color 0.3s ease !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.nav-menu a:hover {
  color: #4f7fe8 !important;
}

/* 当前页面菜单项高亮 */
.nav-menu .current-menu-item > a,
.nav-menu .current_page_item > a {
  color: #4f7fe8 !important;
}

/* 桌面端二级菜单样式 - 定位在header下方 */
@media (min-width: 769px) {
  .nav-menu .sub-menu {
    position: fixed !important;
    top: 70px !important;
    left: auto !important;
    background: #fff !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-radius: 4px !important;
    border-top: 3px solid #4f7fe8 !important;
    min-width: 160px !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s ease !important;
    z-index: 1001 !important;
    list-style: none !important;
    margin: 0 !important;
    padding: 10px 0 !important;
  }
}

/* 桌面端二级菜单hover效果 */
@media (min-width: 769px) {
  .nav-menu li:hover .sub-menu {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
  }
}

/* 桌面端二级菜单项样式 */
@media (min-width: 769px) {
  .nav-menu .sub-menu li {
    display: block !important;
    width: 100% !important;
  }

  .nav-menu .sub-menu a {
    padding: 10px 16px !important;
    color: #666 !important;
    font-size: 14px !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .nav-menu .sub-menu a:hover {
    background: #f8f9fa !important;
    color: #4f7fe8 !important;
  }
}

/* 移动端菜单按钮 */
.menu-toggle {
  display: none !important;
  cursor: pointer !important;
  padding: 10px !important;
  border-radius: 4px !important;
  transition: background-color 0.3s ease !important;
}

.menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.menu-btn {
  transition: transform 0.3s ease !important;
}

/* 移动端样式 - 强制隐藏菜单 */
@media (max-width: 768px) {
  .menu-toggle {
    display: block !important;
  }

  /* 强制隐藏移动端菜单 - 最高优先级 */
  .header .header-r .nav-menu {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: #fff !important;
    flex-direction: column !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    padding: 20px !important;
    gap: 0 !important;
    z-index: 1000 !important;
    border-radius: 0 0 8px 8px !important;
  }

  /* 只有在激活状态下才显示 */
  .header .header-r .nav-menu.active {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .nav-menu li {
    border-bottom: 1px solid #eee !important;
    width: 100% !important;
  }

  .nav-menu li:last-child {
    border-bottom: none !important;
  }

  .nav-menu a {
    padding: 15px 0 !important;
    font-size: 16px !important;
    color: #333 !important;
  }

  .nav-menu a:hover {
    color: #4f7fe8 !important;
  }

  /* 移动端二级菜单 */
  .nav-menu .sub-menu {
    position: static !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
    box-shadow: none !important;
    background: #f8f9fa !important;
    margin: 10px 0 0 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    min-width: auto !important;
  }

  .nav-menu .sub-menu li {
    border-bottom: 1px solid #e9ecef !important;
  }

  .nav-menu .sub-menu a {
    padding: 12px 20px !important;
    font-size: 14px !important;
    color: #666 !important;
  }

  .nav-menu .sub-menu a:hover {
    background: #e9ecef !important;
  }
}

/* 最终强制规则 - 确保移动端菜单隐藏 */
@media screen and (max-width: 768px) {
  body .header .header-r ul.nav-menu:not(.active) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }

  body .header .header-r ul.nav-menu.active {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* 调试样式已移除 - 菜单正常工作 */

/* 强制显示菜单 - 紧急修复 */
.header-r .nav-menu {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 999 !important;
}
