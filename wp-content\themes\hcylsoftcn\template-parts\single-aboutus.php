<?php

/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package hcylsoftcn
 */

//get_header();
?>
<div class="wrapper-pad">
    <div class="tabs-content">
        <!-- 公司介绍开始 -->
        <div class="about-content-main dis-block">
            <div class="about-company-box">
                <div class="about-company about-company-vertical">
                    <div class="company-msg-box company-msg-box-vertical">
                        <p class="company-name">华创云联</p>
                        <p class="company-purpose">围绕客户需求持续创新</p>
                        <p class="company-target">国内领先的软件基础平台解决方案与产品提供商</p>
                        <p class="about-line"></p>
                    </div>
                    <div class="company-intro company-intro-vertical">
                        <p class="company-sub mb-only">国内领先的软件基础平台解决方案与产品提供商</p>
                        <p class="company-intro-content">
                            <?php
                            echo get_theme_mod('aboutus_company_intro');
                            ?>
                        </p>
                    </div>
                </div>
            </div>
            <style>
                /* 美化的上下布局样式 */
                /* 第一个区域 - 白色背景 */
                .about-company-vertical {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    min-height: auto !important;
                    /* background: #ffffff; */
                    position: relative;
                    /* padding: 80px 0; */
                }

                .company-msg-box-vertical {
                    position: static !important;
                    width: 100% !important;
                    transform: none !important;
                    text-align: center;
                    margin-bottom: 50px;
                    padding: 60px 40px 40px;
                    /* background: #ffffff; */
                    /* border-radius: 20px; */
                    /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); */
                    margin: 0 auto 50px;
                    max-width: 800px;
                    position: relative;
                    /* border: 1px solid #e9ecef; */
                }



                .company-msg-box-vertical .company-name {
                    font-size: 42px !important;
                    font-weight: 700 !important;
                    background: linear-gradient(135deg, #4f7fe8, #97b5f7);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    margin-bottom: 20px !important;
                    letter-spacing: 2px;
                }

                .company-msg-box-vertical .company-purpose {
                    font-size: 20px !important;
                    color: #6c757d !important;
                    margin-bottom: 15px !important;
                    font-weight: 500;
                }

                .company-msg-box-vertical .company-target {
                    font-size: 18px !important;
                    color: #495057 !important;
                    margin-top: 25px !important;
                    font-weight: 400;
                    line-height: 1.6 !important;
                }

                .company-msg-box-vertical .about-line {
                    width: 120px !important;
                    height: 4px !important;
                    background: linear-gradient(90deg, #4f7fe8, #97b5f7) !important;
                    margin: 30px auto 0 !important;
                    border-radius: 2px;
                    box-shadow: 0 2px 10px rgba(79, 127, 232, 0.3);
                }

                .company-intro-vertical {
                    width: 100% !important;
                    /* margin-left: 0 !important; */
                    margin-top: 0;
                    background: linear-gradient(135deg, #4f7fe8 0%, #6c8ef5 100%) !important;
                    border-radius: 20px;
                    box-shadow: 0 15px 50px rgba(79, 127, 232, 0.2);
                    position: relative;
                    overflow: hidden;
                    margin: 0 auto;
                    max-width: 1000px;
                }

                .company-intro-vertical::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                    opacity: 0.3;
                    z-index: 1;
                }

                .company-intro-vertical .company-intro-content {
                    position: relative;
                    z-index: 2;
                    font-size: 16px !important;
                    line-height: 1.8 !important;
                    color: rgba(255, 255, 255, 0.95) !important;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .wrapper-pad {
                        padding-top: 0 !important;
                    }

                    .about-company-vertical {
                        padding: 20px 0 !important;
                    }

                    .company-msg-box-vertical {
                        padding: 30px 15px 25px !important;
                        margin-bottom: 25px !important;
                        border-radius: 15px;
                        max-width: 100% !important;
                    }

                    .company-msg-box-vertical .company-name {
                        font-size: 28px !important;
                        letter-spacing: 1px !important;
                        margin-bottom: 15px !important;
                    }

                    .company-msg-box-vertical .company-purpose {
                        font-size: 16px !important;
                        margin-bottom: 12px !important;
                    }

                    .company-msg-box-vertical .company-target {
                        font-size: 14px !important;
                        margin-top: 20px !important;
                        line-height: 1.5 !important;
                    }

                    .company-msg-box-vertical .about-line {
                        width: 80px !important;
                        height: 3px !important;
                        margin: 20px auto 0 !important;
                    }

                    .company-intro-vertical {
                        border-radius: 15px;
                        max-width: 100% !important;
                        margin: 0 15px !important;
                    }

                    .company-intro-vertical .company-intro-content {
                        font-size: 14px !important;
                        line-height: 1.6 !important;
                        padding: 25px 20px !important;
                    }

                    .company-sub.mb-only {
                        display: block !important;
                        font-size: 16px !important;
                        color: #4f7fe8 !important;
                        text-align: center;
                        margin-bottom: 15px !important;
                        font-weight: 600;
                        padding: 0 15px;
                    }
                }

                /* 动画效果 */
                .company-msg-box-vertical {
                    animation: fadeInUp 0.8s ease-out;
                }

                .company-intro-vertical {
                    animation: fadeInUp 0.8s ease-out 0.2s both;
                }

                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }

                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                /* ========== 整个页面全面美化样式 ========== */

                /* 全局页面样式 - 简洁风格 */
                .wrapper-pad {
                    background: #ffffff;
                    min-height: 100vh;
                    position: relative;
                }

                .tabs-content {
                    position: relative;
                    z-index: 1;
                }

                /* 第二个区域 - 淡灰色背景 */
                .about-content-main:nth-child(1) {
                    background: #ffffff;
                }

                .about-content-main:nth-child(2) {
                    background: #f8f9fa;
                    padding: 80px 0;
                }

                .about-content-main:nth-child(3) {
                    background: #ffffff;
                    padding: 80px 0;
                }

                .about-content-main:nth-child(4) {
                    background: #f8f9fa;
                    padding: 80px 0;
                }

                .about-content-main:nth-child(5) {
                    background: #ffffff;
                    padding: 80px 0;
                }

                /* 业务板块区域美化 */
                .company-business-box {
                    background: #ffffff;
                    border-radius: 20px;
                    padding: 60px 40px;
                    margin: 0 auto;
                    max-width: 1200px;
                    position: relative;
                }



                .company-business-title {
                    margin-bottom: 50px;
                    position: relative;
                }

                .business-title-e {
                    font-size: 48px !important;
                    font-weight: 900 !important;
                    background: linear-gradient(135deg, rgba(79, 127, 232, 0.1), rgba(151, 181, 247, 0.1));
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    letter-spacing: 3px;
                }

                .business-title-c {
                    font-size: 28px !important;
                    color: #2c3e50 !important;
                    font-weight: 600 !important;
                    margin-top: 10px;
                }

                .business-img {
                    position: relative;
                    border-radius: 20px;
                    overflow: hidden;
                }

                /* 公司资源区域美化 */
                .company-resources-box {
                    background: #ffffff;
                    margin: 0;
                    width: 100%;
                    position: relative;
                    display: flex;
                    justify-content: center;
                    align-items: flex-start;
                    gap: 40px;
                    flex-wrap: wrap;
                }



                .company-resources-item {
                    background: #ffffff;
                    border-radius: 15px;
                    padding: 40px 30px;
                    margin-bottom: 0;
                    /* box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06);
                    border: 1px solid #e9ecef; */
                    transition: all 0.3s ease;
                    position: relative;
                    flex: 1;
                    min-width: 300px;
                    max-width: 350px;
                    text-align: center;
                }

                .company-resources-item::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #4f7fe8, #97b5f7);
                    transform: scaleX(0);
                    transition: transform 0.3s ease;
                    border-radius: 15px 15px 0 0;
                }



                .company-resources-item img {
                    width: 50px;
                    height: 45px;
                    margin-bottom: 25px;
                    transition: transform 0.3s ease;
                }

                .company-resources-item:hover img {
                    transform: scale(1.1) rotate(5deg);
                }

                .resources-item-summary {
                    font-size: 22px !important;
                    font-weight: 600 !important;
                    color: #2c3e50 !important;
                    margin-bottom: 20px !important;
                }

                .resources-item-detail {
                    font-size: 15px !important;
                    line-height: 1.8 !important;
                    color: #5a6c7d !important;
                    text-align: justify !important;
                }

                /* 团队区域美化 - 简洁风格 */
                .about-content-bg {
                    background: inherit !important;
                    position: relative;
                }

                .qualifications-title {
                    text-align: center;
                    padding: 0 0 60px;
                    position: relative;
                }

                .qualifications-title-c {
                    font-size: 36px !important;
                    font-weight: 700 !important;
                    color: #2c3e50 !important;
                    display: block;
                    margin-bottom: 15px;
                }

                .qualifications-title-e {
                    font-size: 18px !important;
                    color: #6c757d !important;
                    font-weight: 300 !important;
                    letter-spacing: 2px;
                    text-transform: uppercase;
                }

                .qualifications-line {
                    width: 80px !important;
                    height: 4px !important;
                    background: linear-gradient(90deg, #4f7fe8, #97b5f7) !important;
                    margin: 30px auto 0 !important;
                    border-radius: 2px;
                    box-shadow: 0 2px 10px rgba(79, 127, 232, 0.3);
                }

                .team-intro {
                    background: #ffffff;
                    /* margin: 0 auto 60px; */
                    width: 100%;
                    /* max-width: 1000px; */
                    padding: 50px;
                    border-radius: 20px;
                    /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); */
                    position: relative;
                    /* border: 1px solid #e9ecef; */
                }

                .about-company-content {
                    font-size: 16px !important;
                    line-height: 1.8 !important;
                    color: #4a5568 !important;
                    text-align: justify;
                    text-indent: 2em;
                    margin: 0 auto;
                    max-width: 1000px;
                }

                /* 团队图片区域美化 */
                .team-img {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 0 40px 0;
                    position: relative;
                }

                .team-img li {
                    margin-bottom: 20px;
                    list-style: none;
                }

                .team-box {
                    background: #ffffff;
                    padding: 40px 30px;
                    border-radius: 15px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                    border: 1px solid #e9ecef;
                }

                .team-title {
                    font-size: 24px !important;
                    font-weight: 600 !important;
                    color: #2c3e50 !important;
                    margin-bottom: 15px !important;
                }

                .team-content {
                    font-size: 16px !important;
                    color: #5a6c7d !important;
                    line-height: 1.6 !important;
                }

                .team-line {
                    width: 60px !important;
                    height: 3px !important;
                    background: linear-gradient(90deg, #4f7fe8, #97b5f7) !important;
                    margin-top: 20px !important;
                    border-radius: 2px;
                }

                .imgbox {
                    border-radius: 15px;
                    overflow: hidden;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                    transition: transform 0.3s ease;
                    height: 100%;
                    border: 1px solid #e9ecef;
                }

                .imgbox:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
                }

                .imgbox img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: transform 0.3s ease;
                }

                .imgbox:hover img {
                    transform: scale(1.05);
                }

                /* 办公环境标题区域美化 */
                .office-environment-title {
                    /* background: #ffffff; */
                    margin: 0 auto 50px;
                    max-width: 800px;
                    padding: 50px 40px;
                    border-radius: 20px;
                    /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); */
                    position: relative;
                    text-align: center;
                    /* border: 1px solid #e9ecef; */
                }

                .office-title-box {
                    position: relative;
                }

                .office-title-e {
                    font-size: 36px !important;
                    font-weight: 900 !important;
                    background: linear-gradient(135deg, rgba(79, 127, 232, 0.15), rgba(151, 181, 247, 0.15));
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    letter-spacing: 3px;
                    margin-bottom: 15px;
                    text-transform: uppercase;
                }

                .office-title-c {
                    font-size: 32px !important;
                    font-weight: 700 !important;
                    color: #2c3e50 !important;
                    margin-bottom: 25px;
                    background: linear-gradient(135deg, #4f7fe8, #97b5f7);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                .office-title-line {
                    width: 100px !important;
                    height: 4px !important;
                    background: linear-gradient(90deg, #4f7fe8, #97b5f7) !important;
                    margin: 0 auto 25px !important;
                    border-radius: 2px;
                    box-shadow: 0 2px 10px rgba(79, 127, 232, 0.3);
                }

                .office-subtitle {
                    font-size: 18px !important;
                    color: #5a6c7d !important;
                    font-weight: 400;
                    line-height: 1.6;
                    margin: 0;
                }

                /* 办公环境图片画廊优化 */
                .office-gallery {
                    display: grid;
                    grid-template-columns: repeat(12, 1fr);
                    gap: 20px;
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 0 40px;
                }

                .office-gallery li:nth-child(1) {
                    grid-column: span 6;
                }

                .office-gallery li:nth-child(2) {
                    grid-column: span 6;
                }

                .office-gallery li:nth-child(3) {
                    grid-column: span 4;
                }

                .office-gallery li:nth-child(4) {
                    grid-column: span 4;
                }

                .office-gallery li:nth-child(5) {
                    grid-column: span 4;
                }

                .office-gallery li:nth-child(6) {
                    grid-column: span 12;
                    margin-top: 10px;
                }

                .office-gallery .imgbox {
                    height: 250px;
                }

                .office-gallery li:nth-child(6) .imgbox {
                    height: 300px;
                }

                /* 资质证书区域美化 */
                .qualifications-certificate-box {
                    background: #ffffff;
                    margin: 0 auto 0;
                    max-width: 1200px;
                    padding: 60px;
                    border-radius: 20px;
                    /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08); */
                    position: relative;
                    /* border: 1px solid #e9ecef; */
                }

                /* 响应式优化 */
                @media (max-width: 768px) {

                    /* 整体页面布局 */
                    .about-content-main {
                        padding: 30px 0 !important;
                    }

                    .about-content-main:nth-child(2),
                    .about-content-main:nth-child(3),
                    .about-content-main:nth-child(4),
                    .about-content-main:nth-child(5) {
                        padding: 30px 0 !important;
                    }

                    /* 业务板块区域 */
                    .company-business-box {
                        margin: 0 15px !important;
                        padding: 30px 15px !important;
                        border-radius: 15px !important;
                        max-width: none !important;
                    }

                    .company-business-title {
                        margin-bottom: 30px !important;
                    }

                    .business-title-e {
                        font-size: 24px !important;
                        letter-spacing: 1px !important;
                    }

                    .business-title-c {
                        font-size: 18px !important;
                        margin-top: 8px !important;
                    }

                    .business-img img {
                        width: 100% !important;
                        height: auto !important;
                    }

                    /* 公司资源区域 */
                    .company-resources-box {
                        padding: 30px 15px !important;
                        flex-direction: column !important;
                        gap: 20px !important;
                    }

                    .crbr {
                        flex-direction: column !important;
                        gap: 20px !important;
                    }

                    .company-resources-item {
                        min-width: auto !important;
                        max-width: none !important;
                        width: 100% !important;
                        margin: 0 !important;
                        padding: 25px 20px !important;
                        border-radius: 12px !important;
                    }

                    .company-resources-item img {
                        width: 40px !important;
                        height: 36px !important;
                        margin-bottom: 20px !important;
                    }

                    .resources-item-summary {
                        font-size: 18px !important;
                        margin-bottom: 15px !important;
                    }

                    .resources-item-detail {
                        font-size: 14px !important;
                        line-height: 1.6 !important;
                    }

                    /* 团队介绍区域 */
                    .team-intro {
                        margin: 0 15px !important;
                        padding: 25px 20px !important;
                        border-radius: 15px !important;
                        max-width: none !important;
                    }

                    .about-company-content {
                        font-size: 14px !important;
                        line-height: 1.6 !important;
                        text-indent: 1.5em !important;
                        max-width: none !important;
                    }

                    /* 标题区域 */
                    .qualifications-title {
                        padding: 0 15px 40px !important;
                    }

                    .qualifications-title-c {
                        font-size: 24px !important;
                        margin-bottom: 10px !important;
                    }

                    .qualifications-title-e {
                        font-size: 14px !important;
                        letter-spacing: 1px !important;
                    }

                    .qualifications-line {
                        width: 60px !important;
                        height: 3px !important;
                        margin: 20px auto 0 !important;
                    }

                    /* 办公环境标题 */
                    .office-environment-title {
                        margin: 0 15px 25px !important;
                        padding: 25px 20px !important;
                        border-radius: 15px !important;
                        max-width: none !important;
                    }

                    .office-title-e {
                        font-size: 20px !important;
                        letter-spacing: 1px !important;
                        margin-bottom: 10px !important;
                    }

                    .office-title-c {
                        font-size: 20px !important;
                        margin-bottom: 20px !important;
                    }

                    .office-title-line {
                        width: 60px !important;
                        height: 3px !important;
                        margin: 0 auto 20px !important;
                    }

                    .office-subtitle {
                        font-size: 14px !important;
                        line-height: 1.5 !important;
                    }

                    /* 办公环境图片 */
                    .office-gallery {
                        display: block !important;
                        padding: 0 15px 40px !important;
                        max-width: none !important;
                    }

                    .office-gallery li {
                        margin-bottom: 15px !important;
                        width: 100% !important;
                    }

                    .office-gallery .imgbox {
                        height: 180px !important;
                        border-radius: 12px !important;
                    }

                    /* 团队图片区域 */
                    .team-img {
                        padding: 0 15px 40px !important;
                        max-width: none !important;
                    }

                    /* 资质证书区域 */
                    .qualifications-certificate-box {
                        margin: 0 15px !important;
                        padding: 25px 20px !important;
                        border-radius: 15px !important;
                        max-width: none !important;
                    }
                }

                /* 超小屏幕优化 (480px以下) */
                @media (max-width: 480px) {

                    /* 公司介绍区域 */
                    .company-msg-box-vertical {
                        padding: 25px 10px 20px !important;
                        margin-bottom: 20px !important;
                    }

                    .company-msg-box-vertical .company-name {
                        font-size: 24px !important;
                        letter-spacing: 0.5px !important;
                    }

                    .company-msg-box-vertical .company-purpose {
                        font-size: 14px !important;
                    }

                    .company-msg-box-vertical .company-target {
                        font-size: 13px !important;
                        margin-top: 15px !important;
                    }

                    .company-intro-vertical {
                        margin: 0 10px !important;
                        border-radius: 12px !important;
                    }

                    .company-intro-vertical .company-intro-content {
                        font-size: 13px !important;
                        padding: 20px 15px !important;
                    }

                    /* 业务板块区域 */
                    .company-business-box {
                        margin: 0 10px !important;
                        padding: 25px 10px !important;
                    }

                    .business-title-e {
                        font-size: 20px !important;
                    }

                    .business-title-c {
                        font-size: 16px !important;
                    }

                    /* 公司资源区域 */
                    .company-resources-box {
                        padding: 25px 10px !important;
                    }

                    .company-resources-item {
                        padding: 20px 15px !important;
                    }

                    .resources-item-summary {
                        font-size: 16px !important;
                    }

                    .resources-item-detail {
                        font-size: 13px !important;
                    }

                    /* 团队介绍区域 */
                    .team-intro {
                        margin: 0 10px !important;
                        padding: 20px 15px !important;
                    }

                    .about-company-content {
                        font-size: 13px !important;
                        text-indent: 1em !important;
                    }

                    /* 标题区域 */
                    .qualifications-title {
                        padding: 0 10px 30px !important;
                    }

                    .qualifications-title-c {
                        font-size: 20px !important;
                    }

                    .qualifications-title-e {
                        font-size: 12px !important;
                    }

                    /* 办公环境 */
                    .office-environment-title {
                        margin: 0 10px 20px !important;
                        padding: 20px 15px !important;
                    }

                    .office-title-e {
                        font-size: 18px !important;
                    }

                    .office-title-c {
                        font-size: 18px !important;
                    }

                    .office-subtitle {
                        font-size: 13px !important;
                    }

                    .office-gallery {
                        padding: 0 10px 30px !important;
                    }

                    .office-gallery .imgbox {
                        height: 160px !important;
                    }

                    /* 团队图片 */
                    .team-img {
                        padding: 0 10px 30px !important;
                    }

                    /* 资质证书 */
                    .qualifications-certificate-box {
                        margin: 0 10px !important;
                        padding: 20px 15px !important;
                    }
                }

                /* 页面加载动画 */
                .about-content-main {
                    animation: fadeInSection 1s ease-out;
                }

                .about-content-main:nth-child(2) {
                    animation-delay: 0.2s;
                }

                .about-content-main:nth-child(3) {
                    animation-delay: 0.4s;
                }

                .about-content-main:nth-child(4) {
                    animation-delay: 0.6s;
                }

                @keyframes fadeInSection {
                    from {
                        opacity: 0;
                        transform: translateY(50px);
                    }

                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .crbr {
                    max-width: 1200px;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                }

                /* 移动端专用样式优化 */
                @media (max-width: 768px) {

                    /* 移动端图片优化 */
                    .about-img.mb-only {
                        width: 100% !important;
                        padding: 0 15px !important;
                        margin-bottom: 20px !important;
                    }

                    .about-img.mb-only img {
                        width: 100% !important;
                        height: auto !important;
                        border-radius: 12px !important;
                    }

                    /* 移动端团队图片 */
                    .team-img.mb-only {
                        padding: 0 15px 30px !important;
                    }

                    .team-img.mb-only li {
                        margin-bottom: 15px !important;
                    }

                    .team-img.mb-only .imgBox {
                        border-radius: 12px !important;
                        overflow: hidden !important;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
                    }

                    .team-img.mb-only .imgBox img {
                        width: 100% !important;
                        height: auto !important;
                        display: block !important;
                    }

                    /* 移动端业务标题 */
                    .company-business-title.mb-only {
                        padding: 20px 15px !important;
                        margin-bottom: 20px !important;
                        background: rgba(79, 127, 232, 0.05) !important;
                        border-radius: 12px !important;
                        margin: 0 15px 20px !important;
                    }

                    .company-business-title.mb-only .business-title-e {
                        font-size: 18px !important;
                        color: rgba(79, 127, 232, 0.6) !important;
                        margin-bottom: 8px !important;
                    }

                    .company-business-title.mb-only .business-title-c {
                        font-size: 16px !important;
                        color: #2c3e50 !important;
                        position: static !important;
                        margin-top: 0 !important;
                    }

                    /* 移动端团队框 */
                    .team-box.mb-only {
                        background: rgba(79, 127, 232, 0.05) !important;
                        padding: 20px 15px !important;
                        border-radius: 12px !important;
                        margin: 0 15px 20px !important;
                        box-shadow: none !important;
                        border: none !important;
                    }

                    /* 隐藏PC端内容，显示移动端内容 */
                    .pc-pad {
                        display: none !important;
                    }

                    .mb-only {
                        display: block !important;
                    }

                    /* 移动端栅格系统优化 */
                    .col-xs-12 {
                        width: 100% !important;
                        padding: 0 !important;
                    }

                    .col-xs-6 {
                        width: 50% !important;
                        padding: 0 5px !important;
                        box-sizing: border-box !important;
                    }
                }
            </style>
            <div class="company-business-box">
                <div class="company-business-title">
                    <p class="business-title-e">THREE MAJOR BUSINSSSES</p>
                    <p class="business-title-c">公司发展三大业务板块</p>
                </div>
                <div class="business-img">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/business.png" alt="" />
                </div>
            </div>
            <div class="company-resources-box">
                <div class="crbr">
                    <div class="company-resources-item col-md-4 col-sm-4">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/com-1.png" alt="" />
                        <p class="resources-item-summary">高效的技术团队</p>
                        <p class="resources-item-detail">
                            公司拥有众多经验丰富的软件开发工程师、系统维护工程师，他们曾任职于国内知名企业上市公司软件开发中心，长期提供人力外派驻场开发服务。</p>
                    </div>
                    <div class="company-resources-item col-md-4 col-sm-4">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/com-2.png" alt="" />
                        <p class="resources-item-summary">资深的行业经验</p>
                        <p class="resources-item-detail">
                            公司与国内多家知名软件集成商保持着长期稳定的合作关系，客户主要为中大型企事业单位，在北京服务于多家央企，持续稳定为客户提供技术开发与支持服务。</p>
                    </div>
                    <div class="company-resources-item col-md-4 col-sm-4">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/com-3.png" alt="" />
                        <p class="resources-item-summary">良好的合作关系</p>
                        <p class="resources-item-detail">从立项初期即时刻关注客户满意度，及时反馈和解决问题。与客户建立长久良好的合作关系，共同应对工作中面临的各项挑战。
                        </p>
                    </div>
                </div>

            </div>
        </div><!-- 公司介绍结束 -->
        <!-- 核心团队开始 -->
        <div class="about-content-main dis-block">
            <div class="about-content-bg">
                <div class="qualifications-title pc-pad">
                    <span class="qualifications-title-c">我们的团队</span>
                    <span class="qualifications-title-e">Our Team</span><br>
                    <span class="qualifications-line"></span>
                </div>
                <div class="about-img mb-only">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/team.png" alt="" />
                </div>
                <div class="team-intro">
                    <!-- <p class="company-sub">我们的团队</p> -->
                    <p class="about-company-content">
                        作为国内领先的软件基础平台与解决方案提供商，主要面向大中型企业、政府机构及软件开发商提供SOA、大数据、云计算三大领域的软件基础平台及解决方案，为企业用户提供更多元化、更富创造力的解决方案抱有着极大的热情。为此，我们提供良好且舒适的办公环境，优渥的员工福利待遇，吸引了许多经验丰富的技术成员，以及深耕移动互联多年的商务管理人士，并重金聘请技术大神坐镇各开发团队，带领团队专注打磨企业产品。并且，我们极为重视团队协作氛围，通过丰富的团队建设活动，让团队成员之间的沟通更加透明高效、协作流畅。此外，我们拥有着完善的员工培养计划，定期开展企业文化活动、对员工进行知识体系的深化培训、让每一位团队成员都能深挖自己的潜力，取长补短，极大发挥团队合作力量。我们始终以技术为先，不断地锐意进取，创新优化，紧跟移动互联网商业发展趋势，臻化我们的企业云服务，带领更多企业简单、高效、成功地走好移动化转型道路。如果你在渴望拥有一个提升自我的台阶，华创云联，这个充满激情、活力充沛、释放想象力的团队，将是你最好的选择。我们要用技术实力宣告，用更少的时间、更少的人力、更完美的技术支撑、更富创意的解决方案，实现改变世界的产品！从这起初的第一步，迈出加入我们的步伐吧！
                    </p>
                </div>
                <!-- 办公环境标题区域 -->
                <div class="office-environment-title">
                    <div class="office-title-box">
                        <p class="office-title-e">OFFICE ENVIRONMENT</p>
                        <p class="office-title-c">办公环境</p>
                        <p class="office-title-line"></p>
                        <p class="office-subtitle">在这里你可以更快的融入我们</p>
                    </div>
                </div>

                <!-- 办公环境图片区域 -->
                <ul class="team-img pc-pad office-gallery">
                    <li class="col-md-6 col-sm-6">
                        <div class="imgbox"><img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/team-1.png" alt="" /></div>
                    </li>
                    <li class="col-md-6 col-sm-6">
                        <div class="imgbox"><img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/team-2.png" alt="" /></div>
                    </li>
                    <li class="col-md-4 col-sm-4">
                        <div class="imgbox"><img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/team-3.png" style="height: 100%;" alt="" />
                        </div>
                    </li>
                    <li class="col-md-4 col-sm-4">
                        <div class="imgbox"><img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/team-4.png" style="height: 100%;" alt="" />
                        </div>
                    </li>
                    <li class="col-md-4 col-sm-4">
                        <div class="imgbox"><img src="<?= get_stylesheet_directory_uri() ?>/assets/img/about/team-5.png" alt="" /></div>
                    </li>

                </ul>
                <ul class="team-img mb-only">
                    <li class="col-xs-12">
                        <div class="team-box company-business-title mb-only"
                            style="padding-top:15px;margin-bottom:15px;">
                            <p class="business-title-e">Office Environment</p>
                            <p class="business-title-c" style="top:25px;">办公环境</p>
                        </div>
                    </li>
                    <li class="col-xs-6">
                        <div class="imgBox">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/team-1.png" alt="" />
                        </div>
                    </li>
                    <li class="col-xs-6">
                        <div class="imgBox">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/team-2.png" alt="" />
                        </div>
                    </li>
                    <li class="col-xs-6">
                        <div class="imgBox">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/team-3.png" alt="" />
                        </div>
                    </li>
                    <li class="col-xs-6">
                        <div class="imgBox">
                            <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/about/team-4.png" alt="" />
                        </div>
                    </li>
                </ul>
            </div>
        </div><!-- 核心团队结束 -->
        <!-- 公司资质开始 -->
        <div class="about-content-main dis-block">
            <div class="about-content-bg">
                <div class="qualifications-title pc-pad">
                    <span class="qualifications-title-c">公司执照资质</span>
                    <span class="qualifications-title-e">Company License Qualification</span><br>
                    <p class="qualifications-line"></p>
                </div>
                <div class="company-business-title mb-only" style="padding-top:15px;margin-bottom:15px;">
                    <p class="business-title-e">Company License Qualification</p>
                    <p class="business-title-c" style="top:25px;">公司执照资质</p>
                </div>
                <div class="qualifications-certificate-box">
                    <?php
                    $page_slug = 'aptitude'; // 你想要引用内容的页面的别名
                    $page_content = get_page_by_path($page_slug);
                    echo apply_filters('the_content', $page_content->post_content);
                    ?>
                </div>
            </div>
        </div><!-- 公司资质结束 -->
        <!-- 公司发展历程 -->
        <div class="about-content-main dis-block">
            <div class="about-content-bg">
                <div class="qualifications-title pc-pad">
                    <span class="qualifications-title-c">公司发展历程</span>
                    <span class="qualifications-title-e">Company Development History</span><br>
                    <p class="qualifications-line"></p>
                </div>
                <div class="company-business-title mb-only" style="padding-top:15px;margin-bottom:15px;">
                    <p class="business-title-e">Company Development History</p>
                    <p class="business-title-c" style="top:25px;">公司执照资质</p>
                </div>
                <div class="qualifications-certificate-box">
                    <?php
                    $page_slug = 'history'; //
                    $page_content = get_page_by_path($page_slug);
                    echo apply_filters('the_content', $page_content->post_content);
                    ?>
                </div>
            </div>
        </div><!-- 公司资质结束 -->


    </div>
</div>