/* 现代化Footer重新设计样式 */
.footer-main {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #ecf0f1;
  padding: 60px 0 0;
  margin-top: 50px;
  position: relative;
  overflow: hidden;
}

/* 装饰性背景元素 */
.footer-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(79, 127, 232, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: -75px;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 300px;
  gap: 40px;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* 公司品牌区域 */
.footer-section.company-brand {
  min-width: 0;
}

.brand-logo {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.footer-logo-img {
  width: auto;
  height: 40px;
  margin-right: 12px;
  border-radius: 8px;
}

.brand-name {
  color: #4f7fe8;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-slogan {
  color: #bdc3c7;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
  font-style: italic;
}

.brand-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(79, 127, 232, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(79, 127, 232, 0.1);
  transform: translateY(-2px);
}

.stat-number {
  display: block;
  color: #4f7fe8;
  font-size: 18px;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  display: block;
  color: #bdc3c7;
  font-size: 12px;
  margin-top: 4px;
}

/* 联系信息区域 */
.footer-section.contact-info {
  min-width: 0;
}

.footer-title {
  color: #4f7fe8;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 25px;
  padding-bottom: 12px;
  border-bottom: 2px solid #4f7fe8;
  display: flex;
  align-items: center;
  position: relative;
}

.footer-title::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #4f7fe8, #74b9ff);
}

.title-icon {
  margin-right: 8px;
  font-size: 16px;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 3px solid #4f7fe8;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.contact-icon-wrapper {
  width: 36px;
  height: 36px;
  background: rgba(79, 127, 232, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.contact-icon {
  font-size: 16px;
}

.contact-content {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.contact-label {
  color: #74b9ff;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.contact-value {
  color: #ecf0f1;
  font-size: 14px;
  font-weight: 500;
  word-break: break-all;
}

/* 友情链接区域 */
.footer-section.friendly-links {
  min-width: 0;
}

.links-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.friend-link {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.friend-link:hover {
  color: #4f7fe8;
  background: rgba(79, 127, 232, 0.1);
  border-left-color: #4f7fe8;
  transform: translateX(5px);
}

.link-text {
  flex: 1;
}

.link-arrow {
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  font-style: normal;
  color: #4f7fe8;
}

.friend-link:hover .link-arrow {
  opacity: 1;
  transform: translateX(0);
}

/* 二维码区域 */
.footer-section.qr-codes {
  text-align: center;
}

.qr-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.qr-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(79, 127, 232, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.qr-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 127, 232, 0.1), transparent);
  transition: left 0.5s ease;
}

.qr-item:hover::before {
  left: 100%;
}

.qr-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.qr-img-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 12px;
}

.qr-img {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.qr-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(79, 127, 232, 0.9);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.qr-img-wrapper:hover .qr-overlay {
  opacity: 1;
}

.qr-scan-text {
  color: white;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.qr-label {
  margin: 0;
  font-size: 14px;
  color: #ecf0f1;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

/* 现代化版权信息 */
.footer-copy {
  background: linear-gradient(135deg, #0f1419 0%, #1a1a2e 100%);
  padding: 25px 0;
  border-top: 1px solid rgba(79, 127, 232, 0.2);
  margin: 0;
  position: relative;
}

.footer-copy::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #4f7fe8, transparent);
}

.copyright-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.copyright-content {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.copyright-text {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.company-name {
  color: #4f7fe8;
  font-weight: 600;
  font-size: 14px;
}

.rights-text {
  color: #bdc3c7;
  font-size: 14px;
}

.copyright-links {
  display: flex;
  align-items: center;
  gap: 15px;
}

.beian-link {
  color: #74b9ff;
  text-decoration: none;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  /* background: rgba(255, 255, 255, 0.05); */
  border-radius: 20px;
  /* border: 1px solid rgba(79, 127, 232, 0.2); */
  transition: all 0.3s ease;
}

.beian-icon {
  font-size: 12px;
}

.copyright-year {
  color: #7f8c8d;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .footer-container {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
  }

  .footer-section.qr-codes {
    grid-column: 1 / -1;
    margin-top: 20px;
  }

  .qr-container {
    flex-direction: row;
    justify-content: center;
    gap: 30px;
  }
}

@media (max-width: 992px) {
  .footer-container {
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }

  .footer-section.company-brand {
    grid-column: 1 / -1;
  }

  .brand-stats {
    justify-content: center;
  }

  .qr-container {
    flex-direction: row;
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .footer-container {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 15px;
  }

  .footer-main {
    padding: 40px 0 0;
  }

  .brand-logo {
    justify-content: center;
    text-align: center;
  }

  .brand-slogan {
    text-align: center;
  }

  .brand-stats {
    justify-content: center;
    gap: 15px;
  }

  .stat-item {
    padding: 10px 14px;
  }

  .contact-details {
    gap: 12px;
  }

  .contact-item {
    padding: 10px;
  }

  .qr-container {
    flex-direction: row;
    gap: 20px;
    justify-content: center;
  }

  .qr-img {
    width: 100px;
    height: 100px;
  }

  .footer-title {
    font-size: 16px;
    justify-content: center;
    text-align: center;
  }

  .links-grid {
    gap: 6px;
  }

  .friend-link {
    font-size: 13px;
    padding: 8px 10px;
  }

  .copyright-container {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .copyright-content {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-main {
    padding: 30px 0 0;
  }

  .footer-container {
    padding: 0 10px;
    gap: 25px;
  }

  .brand-stats {
    gap: 10px;
  }

  .stat-item {
    padding: 8px 12px;
  }

  .stat-number {
    font-size: 16px;
  }

  .qr-container {
    flex-direction: column;
    gap: 15px;
  }

  .qr-img {
    width: 90px;
    height: 90px;
  }

  .contact-item {
    padding: 8px;
  }

  .contact-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .copyright-text {
    flex-direction: column;
    gap: 5px;
  }

  .decoration-circle {
    display: none;
  }
}

/* 移动端隐藏PC版footer */
@media (max-width: 768px) {
  .footer-main.pc-pad {
    display: none;
  }
}

/* 移动端Footer样式 */
.footer-mobile {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #ecf0f1;
  padding: 20px 0;
  display: none;
}

@media (max-width: 768px) {
  .footer-mobile.mb-only {
    display: block;
  }
}

.mobile-footer-content {
  padding: 0 15px;
  max-width: 100%;
}

.mobile-contact {
  margin-bottom: 20px;
  text-align: center;
}

.mobile-contact h5 {
  color: #4f7fe8;
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
}

.mobile-contact p {
  margin: 5px 0;
  font-size: 13px;
  color: #bdc3c7;
}

.mobile-qr {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.mobile-qr-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 10px;
  border-radius: 6px;
  min-width: 80px;
}

.mobile-qr-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-qr-item span {
  display: block;
  margin-top: 5px;
  font-size: 11px;
  color: #bdc3c7;
}

.mobile-links {
  margin-bottom: 20px;
  text-align: center;
}

.mobile-links h5 {
  color: #4f7fe8;
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
}

.mobile-links-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.mobile-links-grid a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 12px;
  padding: 4px 6px;
  transition: all 0.3s ease;
  text-align: center;
}

.mobile-links-grid a:hover {
  color: #4f7fe8;
  text-decoration: underline;
}

.mobile-copyright {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 11px;
  color: #7f8c8d;
}

.mobile-copyright p {
  margin: 3px 0;
}

.mobile-copyright a {
  color: #4f7fe8;
  text-decoration: none;
}

.mobile-copyright a:hover {
  text-decoration: underline;
}
